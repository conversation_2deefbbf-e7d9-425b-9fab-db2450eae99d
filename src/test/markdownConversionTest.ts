/**
 * Markdown转换功能测试
 * 验证AI生成的Markdown内容能否正确转换为HTML
 */

// 复制BasicEditor中的转换函数用于测试
function markdownToHtml(markdown: string): string {
  if (!markdown || typeof markdown !== "string") {
    return "";
  }

  let html = markdown;

  // 转换标题 (# ## ### 等)
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

  // 转换粗体 **text** 或 __text__
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/__(.*?)__/g, '<strong>$1</strong>');

  // 转换斜体 *text* 或 _text_
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
  html = html.replace(/_(.*?)_/g, '<em>$1</em>');

  // 转换代码块 ```code```
  html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

  // 转换行内代码 `code`
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

  // 转换链接 [text](url)
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

  // 转换无序列表
  html = html.replace(/^\* (.+$)/gim, '<li>$1</li>');
  html = html.replace(/^- (.+$)/gim, '<li>$1</li>');
  
  // 包装连续的li标签为ul
  html = html.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');
  
  // 转换有序列表
  html = html.replace(/^\d+\. (.+$)/gim, '<li>$1</li>');
  
  // 转换换行
  html = html.replace(/\n\n/g, '</p><p>');
  html = html.replace(/\n/g, '<br>');

  // 包装段落
  if (html && !html.startsWith('<')) {
    html = '<p>' + html + '</p>';
  }

  return html;
}

// 测试用例
export function testMarkdownConversion() {
  console.log("🧪 开始测试Markdown转换功能...");

  // 测试1: 标题转换
  const titleMarkdown = "# 主标题\n## 副标题\n### 小标题";
  const titleHtml = markdownToHtml(titleMarkdown);
  console.log("📝 标题转换测试:");
  console.log("输入:", titleMarkdown);
  console.log("输出:", titleHtml);

  // 测试2: 粗体和斜体
  const formatMarkdown = "这是**粗体文本**和*斜体文本*的示例";
  const formatHtml = markdownToHtml(formatMarkdown);
  console.log("📝 格式转换测试:");
  console.log("输入:", formatMarkdown);
  console.log("输出:", formatHtml);

  // 测试3: 列表
  const listMarkdown = "* 第一项\n* 第二项\n* 第三项";
  const listHtml = markdownToHtml(listMarkdown);
  console.log("📝 列表转换测试:");
  console.log("输入:", listMarkdown);
  console.log("输出:", listHtml);

  // 测试4: 代码
  const codeMarkdown = "这是`行内代码`和代码块:\n```\nfunction test() {\n  return 'hello';\n}\n```";
  const codeHtml = markdownToHtml(codeMarkdown);
  console.log("📝 代码转换测试:");
  console.log("输入:", codeMarkdown);
  console.log("输出:", codeHtml);

  // 测试5: 复合内容（模拟AI生成的内容）
  const aiMarkdown = `# AI生成的便签内容

这是一个**重要的**项目管理建议：

## 关键步骤

1. 明确项目目标
2. 制定详细计划
3. 分配资源和责任

### 注意事项

* 定期检查进度
* 及时调整策略
* 保持团队沟通

使用\`项目管理工具\`可以提高效率。

\`\`\`
// 示例代码
const project = {
  name: "新项目",
  status: "进行中"
};
\`\`\`

更多信息请参考[项目管理指南](https://example.com)。`;

  const aiHtml = markdownToHtml(aiMarkdown);
  console.log("📝 AI内容转换测试:");
  console.log("输入:", aiMarkdown);
  console.log("输出:", aiHtml);

  console.log("✅ Markdown转换测试完成");
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，可以在控制台调用
  (window as any).testMarkdownConversion = testMarkdownConversion;
}
