/**
 * AI Markdown 便签测试
 * 模拟AI生成Markdown内容并在便签中显示的场景
 */

import React, { useState } from "react";
import { Button, Card, Space, Typography, message } from "antd";
import { StickyNote } from "../components/types";
import StickyNoteComponent from "../components/notes/StickyNote";

const { Title, Text } = Typography;

const AIMarkdownTest: React.FC = () => {
  const [notes, setNotes] = useState<StickyNote[]>([]);

  // 模拟AI生成的Markdown内容
  const aiGeneratedMarkdown = `# 项目管理最佳实践

这是一个**重要的**项目管理指南，包含以下关键要素：

## 核心原则

1. **明确目标**：确保所有团队成员理解项目目标
2. **有效沟通**：建立清晰的沟通渠道
3. **风险管理**：提前识别和应对潜在风险

### 实施步骤

* 制定详细的项目计划
* 分配合适的资源和人员
* 建立监控和评估机制

使用\`项目管理工具\`可以显著提高效率。

\`\`\`javascript
// 项目状态跟踪示例
const project = {
  name: "新产品开发",
  status: "进行中",
  progress: 75
};
\`\`\`

更多信息请参考[项目管理指南](https://example.com)。`;

  const createAINote = () => {
    const newNote: StickyNote = {
      id: `ai-note-${Date.now()}`,
      x: 100 + notes.length * 50,
      y: 100 + notes.length * 50,
      width: 400,
      height: 300,
      content: aiGeneratedMarkdown,
      title: "AI生成的项目管理指南",
      isEditing: false,
      isTitleEditing: false,
      color: "blue",
      isNew: false,
      zIndex: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      contentFormat: "markdown", // 明确标记为Markdown格式
    };

    setNotes(prev => [...prev, newNote]);
    message.success("AI便签已创建！检查Markdown是否正确显示为格式化文本。");
  };

  const createPlainTextNote = () => {
    const plainContent = `这是普通文本内容，没有任何Markdown格式。
应该显示为纯文本，没有特殊格式。
用于对比测试。`;

    const newNote: StickyNote = {
      id: `plain-note-${Date.now()}`,
      x: 150 + notes.length * 50,
      y: 150 + notes.length * 50,
      width: 300,
      height: 200,
      content: plainContent,
      title: "普通文本便签",
      isEditing: false,
      isTitleEditing: false,
      color: "yellow",
      isNew: false,
      zIndex: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      contentFormat: "markdown",
    };

    setNotes(prev => [...prev, newNote]);
    message.info("普通文本便签已创建，用于对比。");
  };

  const clearNotes = () => {
    setNotes([]);
    message.info("所有便签已清除。");
  };

  const handleNoteUpdate = (updatedNote: StickyNote) => {
    setNotes(prev => prev.map(note => 
      note.id === updatedNote.id ? updatedNote : note
    ));
  };

  const handleNoteDelete = (noteId: string) => {
    setNotes(prev => prev.filter(note => note.id !== noteId));
  };

  return (
    <div style={{ padding: "20px", minHeight: "100vh", backgroundColor: "#f5f5f5" }}>
      <div style={{ maxWidth: "1200px", margin: "0 auto" }}>
        <Title level={2}>🤖 AI Markdown 便签测试</Title>
        <Text type="secondary">
          测试AI生成的Markdown内容在便签中的显示效果
        </Text>

        <Card style={{ marginTop: "20px", marginBottom: "20px" }}>
          <Space>
            <Button type="primary" onClick={createAINote}>
              创建AI Markdown便签
            </Button>
            <Button onClick={createPlainTextNote}>
              创建普通文本便签
            </Button>
            <Button danger onClick={clearNotes}>
              清除所有便签
            </Button>
          </Space>
        </Card>

        <Card title="📋 测试说明" style={{ marginBottom: "20px" }}>
          <div>
            <Text strong>测试目的：</Text>
            <br />
            验证AI生成的Markdown内容能否在便签中正确显示为格式化文本。
            <br />
            <br />
            <Text strong>预期效果：</Text>
            <ul>
              <li>AI便签中的标题应该显示为不同大小的标题样式</li>
              <li>**粗体**文本应该显示为粗体</li>
              <li>列表应该显示为项目符号或数字列表</li>
              <li>`代码`应该显示为等宽字体</li>
              <li>代码块应该显示为格式化的代码区域</li>
              <li>链接应该显示为可点击的链接</li>
            </ul>
            <br />
            <Text strong>如果显示为纯文本：</Text>
            <br />
            说明Markdown转换功能没有正常工作，需要进一步调试。
          </div>
        </Card>

        {/* 便签显示区域 */}
        <div style={{ position: "relative", minHeight: "600px", border: "1px dashed #ccc", borderRadius: "8px", padding: "20px" }}>
          {notes.length === 0 ? (
            <div style={{ textAlign: "center", color: "#999", marginTop: "100px" }}>
              点击上方按钮创建测试便签
            </div>
          ) : (
            notes.map(note => (
              <StickyNoteComponent
                key={note.id}
                note={note}
                onUpdate={handleNoteUpdate}
                onDelete={handleNoteDelete}
                onBringToFront={() => {}}
                canvasScale={1}
                canvasOffset={{ x: 0, y: 0 }}
                isMoveModeActive={false}
                isStreaming={false}
                streamingContent=""
                isConnected={false}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default AIMarkdownTest;
