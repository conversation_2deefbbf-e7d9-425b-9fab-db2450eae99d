/**
 * Markdown转换测试页面
 * 用于验证AI生成的Markdown内容能否正确显示
 */

import React, { useState } from "react";
import { Button, Card, Input, Space, Typography } from "antd";
import BasicEditor from "../components/notes/BasicEditor";

const { Title, Text } = Typography;
const { TextArea } = Input;

const MarkdownTestPage: React.FC = () => {
  const [markdownContent, setMarkdownContent] = useState(`# AI生成的便签内容

这是一个**重要的**项目管理建议：

## 关键步骤

1. 明确项目目标
2. 制定详细计划
3. 分配资源和责任

### 注意事项

* 定期检查进度
* 及时调整策略
* 保持团队沟通

使用\`项目管理工具\`可以提高效率。

\`\`\`javascript
// 示例代码
const project = {
  name: "新项目",
  status: "进行中"
};
\`\`\`

更多信息请参考[项目管理指南](https://example.com)。`);

  const [editorContent, setEditorContent] = useState("");

  const handleTestConversion = () => {
    setEditorContent(markdownContent);
  };

  const handleReset = () => {
    setEditorContent("");
  };

  return (
    <div style={{ padding: "20px", maxWidth: "1200px", margin: "0 auto" }}>
      <Title level={2}>🧪 Markdown转换测试</Title>
      <Text type="secondary">
        测试AI生成的Markdown内容在BasicEditor中的显示效果
      </Text>

      <div style={{ marginTop: "20px" }}>
        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          {/* 输入区域 */}
          <Card title="📝 Markdown输入" size="small">
            <TextArea
              value={markdownContent}
              onChange={(e) => setMarkdownContent(e.target.value)}
              placeholder="输入Markdown内容..."
              rows={10}
              style={{ fontFamily: "monospace" }}
            />
            <div style={{ marginTop: "10px" }}>
              <Space>
                <Button type="primary" onClick={handleTestConversion}>
                  测试转换
                </Button>
                <Button onClick={handleReset}>清空编辑器</Button>
              </Space>
            </div>
          </Card>

          {/* 编辑器显示区域 */}
          <Card title="🎨 BasicEditor显示效果" size="small">
            <div
              style={{
                border: "1px solid #d9d9d9",
                borderRadius: "6px",
                minHeight: "300px",
                padding: "10px",
              }}
            >
              <BasicEditor
                content={editorContent}
                onChange={(newContent) => {
                  console.log("编辑器内容变化:", newContent);
                }}
                placeholder="这里将显示转换后的内容..."
                editable={true}
                showToolbar={false}
              />
            </div>
          </Card>

          {/* 说明文档 */}
          <Card title="📖 测试说明" size="small">
            <div>
              <Text strong>测试目的：</Text>
              <br />
              验证AI生成的Markdown内容能否在BasicEditor中正确显示为格式化文本。
              <br />
              <br />
              <Text strong>预期效果：</Text>
              <ul>
                <li>标题应该显示为不同大小的标题样式</li>
                <li>**粗体**文本应该显示为粗体</li>
                <li>*斜体*文本应该显示为斜体</li>
                <li>列表应该显示为项目符号或数字列表</li>
                <li>`代码`应该显示为等宽字体</li>
                <li>代码块应该显示为格式化的代码区域</li>
                <li>链接应该显示为可点击的链接</li>
              </ul>
              <br />
              <Text strong>如果显示为纯文本：</Text>
              <br />
              说明Markdown转换功能没有正常工作，需要进一步调试。
            </div>
          </Card>
        </Space>
      </div>
    </div>
  );
};

export default MarkdownTestPage;
